import React, { useEffect, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import CartCounter from '../../core/common/counter/counter'
import Select from "react-select";
import { Check } from 'feather-icons-react/build/IconComponents';
import PosModals from '../../core/modals/pos-modal/posModals';
import {
  getCustomersForSelect,
  getSelectedCustomer,
  setSelectedCustomer,
  getOrdersForCustomer
} from "../../core/utils/customerStorage";

const Pos2 = () => {
    const [activeTab, setActiveTab] = useState('all')
    const [showAlert1, setShowAlert1] = useState(true)
    const Location = useLocation();

    // Customer state
    const [customerOptions, setCustomerOptions] = useState([])
    const [selectedCustomer, setSelectedCustomerState] = useState(null);

    // Load customers and selected customer
    useEffect(() => {
        const loadCustomers = () => {
            const customers = getCustomersForSelect();
            setCustomerOptions(customers);

            const selected = getSelectedCustomer();
            setSelectedCustomerState(selected);
        };

        loadCustomers();
    }, []);

    // Listen for customer updates from modals
    useEffect(() => {
        const handleCustomerUpdate = (event) => {
            const { customer } = event.detail;

            // Reload customer options
            const customers = getCustomersForSelect();
            setCustomerOptions(customers);

            // Update selected customer
            setSelectedCustomerState(customer);
        };

        window.addEventListener('customerUpdated', handleCustomerUpdate);

        return () => {
            window.removeEventListener('customerUpdated', handleCustomerUpdate);
        };
    }, []);

    // Handle customer selection change
    const handleCustomerChange = (selectedOption) => {
        if (selectedOption && selectedOption.customer) {
            setSelectedCustomer(selectedOption.customer);
            setSelectedCustomerState(selectedOption.customer);
        }
    };

    // Handle save order button click
    const handleSaveOrderClick = () => {
        if (!selectedCustomer || selectedCustomer.id === 'walk-in') {
            alert('Please select a customer before saving the order.');
            return;
        }

        // Open save order modal
        const modal = new window.bootstrap.Modal(document.getElementById('save-order'));
        modal.show();
    };

    // Handle view customer orders button click
    const handleViewCustomerOrdersClick = () => {
        if (!selectedCustomer || selectedCustomer.id === 'walk-in') {
            alert('Please select a customer to view their orders.');
            return;
        }

        // Load customer orders and open modal
        const orders = getOrdersForCustomer(selectedCustomer.id);

        // Trigger event to update modal state
        window.dispatchEvent(new CustomEvent('loadCustomerOrders', {
            detail: { customer: selectedCustomer, orders: orders }
        }));

        // Open customer orders modal
        const modal = new window.bootstrap.Modal(document.getElementById('customer-orders'));
        modal.show();
    };

    useEffect(() => {
        document.addEventListener("click", function (event) {
            if (event.target.closest(".product-info")) {
                let productInfo = event.target.closest(".product-info");
                productInfo.classList.toggle("active");

                if (document.querySelectorAll(".product-info.active").length > 0) {
                    // If "active" exists, hide .empty-cart and show .product-list
                    document.querySelector(".product-wrap .empty-cart").style.display = "none";
                    document.querySelector(".product-wrap .product-list").style.display = "block";
                } else {
                    // If not "active", reverse the behavior
                    document.querySelector(".product-wrap .empty-cart").style.display = "flex";
                    document.querySelector(".product-wrap .product-list").style.display = "none";
                }
            }
        });
        document.body.classList.add("pos-page");
        return () => {
            document.body.classList.remove("pos-page");
        }

    }, [Location.pathname, showAlert1])


    return (
        <div className='main-wrapper pos-five'>
            <div className="page-wrapper pos-pg-wrapper ms-0">
                <div className="content pos-design p-0">
                    <div className="row pos-wrapper">
                        {/* Products */}
                        <div className="col-md-12 col-lg-7 col-xl-8 d-flex">
                            <div className="pos-categories tabs_wrapper p-0 flex-fill">
                                <div className="content-wrap">
                                    <div className="tab-wrap">
                                        <ul className="tabs owl-carousel pos-category5">
                                            <li id="all" onClick={() => setActiveTab('all')} className={activeTab === 'all' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-01.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">All</Link>
                                                </h6>
                                            </li>
                                            <li id="headphones" onClick={() => setActiveTab('headphones')} className={activeTab === 'headphones' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-02.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Headset</Link>
                                                </h6>
                                            </li>
                                            <li id="shoes" onClick={() => setActiveTab('shoes')} className={activeTab === 'shoes' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-03.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Shoes</Link>
                                                </h6>
                                            </li>
                                            <li id="mobiles" onClick={() => setActiveTab('mobiles')} className={activeTab === 'mobiles' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-04.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Mobiles</Link>
                                                </h6>
                                            </li>
                                            <li id="watches" onClick={() => setActiveTab('watches')} className={activeTab === 'watches' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-05.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Watches</Link>
                                                </h6>
                                            </li>
                                            <li id="laptops" onClick={() => setActiveTab('laptops')} className={activeTab === 'laptops' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-06.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Laptops</Link>
                                                </h6>
                                            </li>
                                            <li id="appliances" onClick={() => setActiveTab('appliances')} className={activeTab === 'appliances' ? 'active' : ''}>
                                                <Link to="#">
                                                    <img
                                                        src="assets/img/categories/category-07.svg"
                                                        alt="Categories"
                                                    />
                                                </Link>
                                                <h6>
                                                    <Link to="#">Appliance</Link>
                                                </h6>
                                            </li>
                                        </ul>
                                    </div>
                                    <div className="tab-content-wrap">
                                        <div className="d-flex align-items-center justify-content-between flex-wrap mb-2">
                                            <div className="mb-3">
                                                <h5 className="mb-1">Welcome, Wesley Adrian</h5>
                                                <p>December 24, 2024</p>
                                            </div>
                                            <div className="d-flex align-items-center flex-wrap mb-2">
                                                <div className="input-icon-start search-pos position-relative mb-2 me-3">
                                                    <span className="input-icon-addon">
                                                        <i className="ti ti-search" />
                                                    </span>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder="Search Product"
                                                    />
                                                </div>
                                                <Link to="#" className="btn btn-sm btn-dark mb-2 me-2">
                                                    <i className="ti ti-tag me-1" />
                                                    View All Brands
                                                </Link>
                                                <Link to="#" className="btn btn-sm btn-primary mb-2">
                                                    <i className="ti ti-star me-1" />
                                                    Featured
                                                </Link>
                                            </div>
                                        </div>
                                        <div className="pos-products">
                                            <div className="tabs_container">
                                                <div className={`tab_content ${activeTab === 'all' ? 'active' : ''} `} data-tab="all">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-01.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Mobiles</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">IPhone 14 64GB</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$15800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-02.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">MacBook Pro</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1000</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-03.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Rolex Tribute V3</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-04.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Red Nike Angelo</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card active mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-05.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Airpod 2</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$5478</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-06.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Blue White OGR</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$987</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-07.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Laptop</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        IdeaPad Slim 5 Gen 7
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1454</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-08.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">SWAGME</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6587</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-09.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Timex Black Silver</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1457</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-10.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Tablet 1.02 inch</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$4744</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-11.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        Fossil Pair Of 3 in 1{" "}
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$789</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-18.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Green Nike Fe</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7847</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'headphones' ? 'active' : ''} `} data-tab="headphones">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-05.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Airpod 2</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$5478</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-08.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <Check className="feather-16" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">SWAGME</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6587</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'shoes' ? 'active' : ''} `} data-tab="shoes">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-04.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Red Nike Angelo</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-06.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Blue White OGR</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$987</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-18.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Green Nike Fe</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7847</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'mobiles' ? 'active' : ''} `} data-tab="mobiles">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-01.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Mobiles</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">IPhone 14 64GB</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$15800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-14.png"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Mobiles</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Iphone 11</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$3654</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'watches' ? 'active' : ''} `} data-tab="watches">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-03.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Rolex Tribute V3</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-09.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Timex Black Silver</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1457</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-11.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        Fossil Pair Of 3 in 1{" "}
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$789</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'laptops' ? 'active' : ''} `} data-tab="laptops">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-02.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">MacBook Pro</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1000</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-07.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Laptop</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        IdeaPad Slim 5 Gen 7
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1454</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-10.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Tablet 1.02 inch</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$4744</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-13.png"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Laptop</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Yoga Book 9i</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$4784</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-14.png"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Laptop</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">IdeaPad Slim 3i</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1245</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={`tab_content ${activeTab === 'appliances' ? 'active' : ''} `} data-tab="appliances">
                                                    <div className="row g-3">
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-01.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Mobiles</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">IPhone 14 64GB</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$15800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-02.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">MacBook Pro</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1000</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-03.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Rolex Tribute V3</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-04.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Red Nike Angelo</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7800</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-05.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Airpod 2</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$5478</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-06.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Blue White OGR</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$987</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-07.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Laptop</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        IdeaPad Slim 5 Gen 7
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1454</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-08.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Headphones</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">SWAGME</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$6587</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-09.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Timex Black Silver</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$1457</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-10.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Computer</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Tablet 1.02 inch</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$4744</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-11.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Watches</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">
                                                                        Fossil Pair Of 3 in 1{" "}
                                                                    </Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$789</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-6 col-md-6 col-lg-6 col-xl-4 col-xxl-3">
                                                            <div className="product-info card mb-0" onClick={() => setShowAlert1(!showAlert1)} tabIndex="0">
                                                                <Link to="#" className="pro-img">
                                                                    <img
                                                                        src="assets/img/products/pos-product-18.svg"
                                                                        alt="Products"
                                                                    />
                                                                    <span>
                                                                        <i className="ti ti-circle-check-filled" />
                                                                    </span>
                                                                </Link>
                                                                <h6 className="cat-name">
                                                                    <Link to="#">Shoes</Link>
                                                                </h6>
                                                                <h6 className="product-name">
                                                                    <Link to="#">Green Nike Fe</Link>
                                                                </h6>
                                                                <div className="d-flex align-items-center justify-content-between price">
                                                                    <p className="text-gray-9 mb-0">$7847</p>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter defaultValue={4} />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/* /Products */}
                        {/* Order Details */}
                        <div className="col-md-12 col-lg-5 col-xl-4 ps-0 theiaStickySidebar d-lg-flex">
                            <aside className="product-order-list bg-secondary-transparent flex-fill">
                                <div className="card">
                                    <div className="card-body">
                                        <div className="order-head d-flex align-items-center justify-content-between w-100">
                                            <div>
                                                <h3>Order List</h3>
                                            </div>
                                            <div className="d-flex align-items-center gap-2">
                                                <span className="badge badge-dark fs-10 fw-medium badge-xs">
                                                    #ORD123
                                                </span>
                                                <Link className="link-danger fs-16" to="#">
                                                    <i className="ti ti-trash-x-filled" />
                                                </Link>
                                            </div>
                                        </div>
                                        <div className="customer-info block-section">
                                            <h5 className="mb-2">Customer Information</h5>
                                            <div className="d-flex align-items-center gap-2">
                                                <div className="flex-grow-1">

                                                    <Select
                                                        options={customerOptions}
                                                        classNamePrefix="react-select select"
                                                        placeholder="Choose a Customer"
                                                        value={customerOptions.find(option => option.customer?.id === selectedCustomer?.id) || null}
                                                        onChange={handleCustomerChange}
                                                        isSearchable={true}
                                                    />
                                                </div>
                                                <Link
                                                    to="#"
                                                    className="btn btn-teal btn-icon fs-20"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#create"
                                                >
                                                    <i className="ti ti-user-plus" />
                                                </Link>
                                                <Link
                                                    to="#"
                                                    className="btn btn-info btn-icon fs-20"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#barcode"
                                                >
                                                    <i className="ti ti-scan" />
                                                </Link>
                                            </div>
                                            {selectedCustomer && selectedCustomer.id !== 'walk-in' && (
                                                <div className="customer-item border border-orange bg-orange-100 d-flex align-items-center justify-content-between flex-wrap gap-2 mt-3">
                                                    <div>
                                                        <h6 className="fs-16 fw-bold mb-1">{selectedCustomer.name}</h6>
                                                        <div className="d-inline-flex align-items-center gap-2 customer-info-details">
                                                            {selectedCustomer.phone && (
                                                                <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                                    <i className="ti ti-phone fs-12"></i>
                                                                    <span className="text-muted">
                                                                        {selectedCustomer.phone}
                                                                    </span>
                                                                </p>
                                                            )}
                                                            {selectedCustomer.email && (
                                                                <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                                    <i className="ti ti-mail fs-12"></i>
                                                                    <span className="text-muted">
                                                                        {selectedCustomer.email}
                                                                    </span>
                                                                </p>
                                                            )}
                                                        </div>
                                                        {selectedCustomer.address && (
                                                            <p className="fs-12 text-muted mb-0">
                                                                <i className="ti ti-map-pin fs-12 me-1"></i>
                                                                {selectedCustomer.address}
                                                            </p>
                                                        )}
                                                    </div>
                                                    <Link to="#" className="close-icon" onClick={() => {
                                                        // Reset to walk-in customer
                                                        const walkInCustomer = customerOptions.find(option => option.customer?.id === 'walk-in')?.customer;
                                                        if (walkInCustomer) {
                                                            setSelectedCustomer(walkInCustomer);
                                                            setSelectedCustomerState(walkInCustomer);
                                                        }
                                                    }}>
                                                        <i className="ti ti-x" />
                                                    </Link>
                                                </div>
                                            )}

                                        </div>
                                        <div className="product-added block-section">
                                            <div className="head-text d-flex align-items-center justify-content-between mb-3">
                                                <div className="d-flex align-items-center">
                                                    <h5 className="me-2">Order Details</h5>
                                                    <div className="badge bg-light text-gray-9 fs-12 fw-semibold py-2 border rounded">
                                                        Items : <span className="text-teal">3</span>
                                                    </div>
                                                </div>
                                                <div className="d-flex align-items-center gap-2">
                                                    {selectedCustomer && selectedCustomer.id !== 'walk-in' && (
                                                        <>
                                                            <Link
                                                                to="#"
                                                                className="d-flex align-items-center fs-10 fw-medium text-primary"
                                                                onClick={handleViewCustomerOrdersClick}
                                                                title="View saved orders for this customer"
                                                            >
                                                                <i className="ti ti-history me-1"></i>
                                                                Orders
                                                            </Link>
                                                            <Link
                                                                to="#"
                                                                className="d-flex align-items-center fs-10 fw-medium text-success"
                                                                onClick={handleSaveOrderClick}
                                                                title="Save current order for this customer"
                                                            >
                                                                <i className="ti ti-device-floppy me-1"></i>
                                                                Save
                                                            </Link>
                                                        </>
                                                    )}
                                                    <Link
                                                        to="#"
                                                        className="d-flex align-items-center clear-icon fs-10 fw-medium"
                                                    >
                                                        Clear all
                                                    </Link>
                                                </div>
                                            </div>
                                            <div className="product-wrap">
                                                <div className="empty-cart">
                                                    <div className="fs-24 mb-1">
                                                        <i className="ti ti-shopping-cart" />
                                                    </div>
                                                    <p className="fw-bold">No Products Selected</p>
                                                </div>
                                                <div className="product-list border-0 p-0">
                                                    <div className="table-responsive">
                                                        <table className="table table-borderless">
                                                            <thead>
                                                                <tr>
                                                                    <th className="fw-bold bg-light">Item</th>
                                                                    <th className="fw-bold bg-light">QTY</th>
                                                                    <th className="fw-bold bg-light text-end">Cost</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <div className="d-flex align-items-center">
                                                                            <Link
                                                                                className="delete-icon"
                                                                                to="#"
                                                                                data-bs-toggle="modal"
                                                                                data-bs-target="#delete"
                                                                            >
                                                                                <i className="ti ti-trash-x-filled" />
                                                                            </Link>
                                                                            <h6 className="fs-13 fw-normal">
                                                                                <Link
                                                                                    to="#"
                                                                                    className=" link-default"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#products"
                                                                                >
                                                                                    iPhone 14 64GB
                                                                                </Link>
                                                                            </h6>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div className="qty-item m-0">
                                                                            <CartCounter defaultValue={1} />
                                                                        </div>
                                                                    </td>
                                                                    <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                        $15800
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <div className="d-flex align-items-center">
                                                                            <Link
                                                                                className="delete-icon"
                                                                                to="#"
                                                                                data-bs-toggle="modal"
                                                                                data-bs-target="#delete"
                                                                            >
                                                                                <i className="ti ti-trash-x-filled" />
                                                                            </Link>
                                                                            <h6 className="fs-13 fw-normal ">
                                                                                <Link
                                                                                    to="#"
                                                                                    className="link-default"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#products"
                                                                                >
                                                                                    Red Nike Angelo
                                                                                </Link>
                                                                            </h6>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div className="qty-item m-0">
                                                                            <CartCounter defaultValue={4} />
                                                                        </div>
                                                                    </td>
                                                                    <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                        $398
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <div className="d-flex align-items-center">
                                                                            <Link
                                                                                className="delete-icon"
                                                                                to="#"
                                                                                data-bs-toggle="modal"
                                                                                data-bs-target="#delete"
                                                                            >
                                                                                <i className="ti ti-trash-x-filled" />
                                                                            </Link>
                                                                            <h6 className="fs-13 fw-normal ">
                                                                                <Link
                                                                                    to="#"
                                                                                    className="link-default"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#products"
                                                                                >
                                                                                    Tablet 1.02 inch
                                                                                </Link>
                                                                            </h6>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div className="qty-item m-0">
                                                                            <CartCounter defaultValue={4} />
                                                                        </div>
                                                                    </td>
                                                                    <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                        $3000
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <div className="d-flex align-items-center">
                                                                            <Link
                                                                                className="delete-icon"
                                                                                to="#"
                                                                                data-bs-toggle="modal"
                                                                                data-bs-target="#delete"
                                                                            >
                                                                                <i className="ti ti-trash-x-filled" />
                                                                            </Link>
                                                                            <h6 className="fs-13 fw-normal ">
                                                                                <Link
                                                                                    to="#"
                                                                                    className="link-default"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#products"
                                                                                >
                                                                                    IdeaPad Slim 3i
                                                                                </Link>
                                                                            </h6>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div className="qty-item m-0">
                                                                            <CartCounter defaultValue={4} />
                                                                        </div>
                                                                    </td>
                                                                    <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                        $3000
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="discount-item d-flex align-items-center justify-content-between  bg-purple-transparent mt-3 flex-wrap gap-2">
                                                <div className="d-flex align-items-center">
                                                    <span className="bg-purple discount-icon br-5 flex-shrink-0 me-2">
                                                        <img src="assets/img/icons/discount-icon.svg" alt="img" />
                                                    </span>
                                                    <div>
                                                        <h6 className="fs-14 fw-bold text-purple mb-1">
                                                            Discount 5%
                                                        </h6>
                                                        <p className="mb-0">
                                                            For $20 Minimum Purchase, all Items
                                                        </p>
                                                        <p></p>
                                                    </div>
                                                </div>
                                                <Link to="#" className="close-icon">
                                                    <i className="ti ti-trash" />
                                                </Link>
                                            </div>
                                        </div>
                                        <div className="order-total bg-total bg-white p-0">
                                            <h5 className="mb-3">Payment Summary</h5>
                                            <table className="table table-responsive table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            Shipping
                                                            <Link
                                                                to="#"
                                                                className="ms-3 link-default"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#shipping-cost"
                                                            >
                                                                <i className="ti ti-edit" />
                                                            </Link>
                                                        </td>
                                                        <td className="text-gray-9 text-end">$40.21</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Tax
                                                            <Link
                                                                to="#"
                                                                className="ms-3 link-default"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#order-tax"
                                                            >
                                                                <i className="ti ti-edit" />
                                                            </Link>
                                                        </td>
                                                        <td className="text-gray-9 text-end">$25</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Coupon
                                                            <Link
                                                                to="#"
                                                                className="ms-3 link-default"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#coupon-code"
                                                            >
                                                                <i className="ti ti-edit" />
                                                            </Link>
                                                        </td>
                                                        <td className="text-gray-9 text-end">$25</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span className="text-danger">Discount</span>
                                                            <Link
                                                                to="#"
                                                                className="ms-3 link-default"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#discount"
                                                            >
                                                                <i className="ti ti-edit" />
                                                            </Link>
                                                        </td>
                                                        <td className="text-danger text-end">$15.21</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <div className="form-check form-switch">
                                                                <input
                                                                    className="form-check-input"
                                                                    type="checkbox"
                                                                    role="switch"
                                                                    id="round"
                                                                    defaultChecked
                                                                />
                                                                <label className="form-check-label" htmlFor="round">
                                                                    Roundoff
                                                                </label>
                                                            </div>
                                                        </td>
                                                        <td className="text-gray-9 text-end">+0.11</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Sub Total</td>
                                                        <td className="text-gray-9 text-end">$60,454</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="fw-bold border-top border-dashed">
                                                            Total Payable
                                                        </td>
                                                        <td className="text-gray-9 fw-bold text-end border-top border-dashed">
                                                            $56590
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div className="card payment-method">
                                    <div className="card-body">
                                        <h5 className="mb-3">Select Payment</h5>
                                        <div className="row align-items-center methods g-2">
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#payment-cash"
                                                >
                                                    <img
                                                        src="assets/img/icons/cash-icon.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Cash</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#payment-card"
                                                >
                                                    <img
                                                        src="assets/img/icons/card.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Card</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#payment-points"
                                                >
                                                    <img
                                                        src="assets/img/icons/points.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Points</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#payment-deposit"
                                                >
                                                    <img
                                                        src="assets/img/icons/deposit.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Deposit</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#payment-cheque"
                                                >
                                                    <img
                                                        src="assets/img/icons/cheque.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Cheque</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#gift-payment"
                                                >
                                                    <img
                                                        src="assets/img/icons/giftcard.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Gift Card</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#scan-payment"
                                                >
                                                    <img
                                                        src="assets/img/icons/scan-icon.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Scan</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                >
                                                    <img
                                                        src="assets/img/icons/paylater.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Pay Later</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                >
                                                    <img
                                                        src="assets/img/icons/external.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">External</p>
                                                </Link>
                                            </div>
                                            <div className="col-sm-6 col-md-4 d-flex">
                                                <Link
                                                    to="#"
                                                    className="payment-item d-flex align-items-center justify-content-center p-2 flex-fill"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#split-payment"
                                                >
                                                    <img
                                                        src="assets/img/icons/split-bill.svg"
                                                        className="me-2"
                                                        alt="img"
                                                    />
                                                    <p className="fs-14 fw-medium">Split Bill</p>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="btn-row d-flex align-items-center justify-content-between gap-3">
                                    <Link
                                        to="#"
                                        className="btn btn-white d-flex align-items-center justify-content-center flex-fill m-0"
                                        data-bs-toggle="modal"
                                        data-bs-target="#hold-order"
                                    >
                                        <i className="ti ti-printer me-2" />
                                        Print Order
                                    </Link>
                                    <Link
                                        to="#"
                                        className="btn btn-secondary d-flex align-items-center justify-content-center flex-fill m-0"
                                    >
                                        <i className="ti ti-shopping-cart me-2" />
                                        Place Order
                                    </Link>
                                </div>
                            </aside>
                        </div>
                        {/* /Order Details */}
                    </div>
                    <div className="pos-footer bg-white p-3 border-top">
                        <div className="d-flex align-items-center justify-content-center flex-wrap gap-2">
                            <Link
                                to="#"
                                className="btn btn-orange d-inline-flex align-items-center justify-content-center"
                                data-bs-toggle="modal"
                                data-bs-target="#hold-order"
                            >
                                <i className="ti ti-player-pause me-2" />
                                Hold
                            </Link>
                            <Link
                                to="#"
                                className="btn btn-info d-inline-flex align-items-center justify-content-center"
                            >
                                <i className="ti ti-trash me-2" />
                                Void
                            </Link>
                            <Link
                                to="#"
                                className="btn btn-cyan d-flex align-items-center justify-content-center"
                                data-bs-toggle="modal"
                                data-bs-target="#payment-completed"
                            >
                                <i className="ti ti-cash-banknote me-2" />
                                Payment
                            </Link>
                            <Link
                                to="#"
                                className="btn btn-secondary d-inline-flex align-items-center justify-content-center"
                                data-bs-toggle="modal"
                                data-bs-target="#orders"
                            >
                                <i className="ti ti-shopping-cart me-2" />
                                View Orders
                            </Link>
                            <Link
                                to="#"
                                className="btn btn-indigo d-inline-flex align-items-center justify-content-center"
                                data-bs-toggle="modal"
                                data-bs-target="#reset"
                            >
                                <i className="ti ti-reload me-2" />
                                Reset
                            </Link>
                            <Link
                                to="#"
                                className="btn btn-danger d-inline-flex align-items-center justify-content-center"
                                data-bs-toggle="modal"
                                data-bs-target="#recents"
                            >
                                <i className="ti ti-refresh-dot me-2" />
                                Transaction
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
            <PosModals />
        </div>


    )
}

export default Pos2
